"""
Vanna AI Configuration for Table Filtering and Business Rules

This module provides configuration options for customizing Vanna AI behavior,
including table whitelisting and context-aware query instructions.
"""

import os
from typing import List, Dict, Any


class VannaConfig:
    """Configuration class for Vanna AI table filtering and business rules"""
    
    # Default table whitelist for BigQuery
    BIGQUERY_ALLOWED_TABLES = [
        'Master_new',
        'PJT_Master'
    ]
    
    # Default table whitelist for PostgreSQL (empty by default)
    POSTGRES_ALLOWED_TABLES = []
    
    # Business rules for different query patterns
    BUSINESS_RULES = {
        'interview_rules': [
            "For interview-related queries, always use Master_new table",
            "When counting interviews, filter by Master_new.candidate_expert containing 'Expert'",
            "Interview data is stored in Master_new table with candidate_expert field",
            "Use LIKE '%Expert%' when filtering for expert candidates"
        ],
        'count_rules': [
            "When counting interviews, use: SELECT COUNT(*) FROM Master_new WHERE candidate_expert LIKE '%Expert%'",
            "When counting projects, use PJT_Master table",
            "Always use appropriate WHERE clauses for counting specific types of records",
            "For number of interviews: COUNT(*) FROM Master_new WHERE candidate_expert LIKE '%Expert%'"
        ],
        'general_rules': [
            "Only query tables: Master_new, PJT_Master",
            "Ignore any temporary tables or tables with _temp, _backup, _test suffixes",
            "Master_new contains candidate and interview information",
            "PJT_Master contains project information",
            "Always use proper table aliases for readability",
            "Use LIMIT for large result sets to avoid performance issues"
        ],
        'field_specific_rules': [
            "candidate_expert field in Master_new: Use LIKE '%Expert%' to find expert candidates",
            "For interview counts: Filter Master_new.candidate_expert containing 'Expert'",
            "Project data: Use PJT_Master table for all project-related queries",
            "Date fields: Use proper date formatting and comparison operators"
        ]
    }
    
    @classmethod
    def get_bigquery_config(cls, custom_tables: List[str] = None, custom_rules: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get BigQuery configuration with optional customizations
        
        Args:
            custom_tables: Optional list of allowed tables to override defaults
            custom_rules: Optional custom business rules to merge with defaults
            
        Returns:
            Dict containing configuration for BigQuery Vanna provider
        """
        allowed_tables = custom_tables if custom_tables is not None else cls.BIGQUERY_ALLOWED_TABLES
        business_rules = cls.BUSINESS_RULES.copy()
        
        if custom_rules:
            for rule_type, rules in custom_rules.items():
                if rule_type in business_rules:
                    business_rules[rule_type].extend(rules)
                else:
                    business_rules[rule_type] = rules
        
        return {
            'allowed_tables': allowed_tables,
            'business_rules': business_rules
        }
    
    @classmethod
    def get_postgres_config(cls, custom_tables: List[str] = None, custom_rules: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        Get PostgreSQL configuration with optional customizations
        
        Args:
            custom_tables: Optional list of allowed tables to override defaults
            custom_rules: Optional custom business rules to merge with defaults
            
        Returns:
            Dict containing configuration for PostgreSQL Vanna provider
        """
        allowed_tables = custom_tables if custom_tables is not None else cls.POSTGRES_ALLOWED_TABLES
        business_rules = cls.BUSINESS_RULES.copy()
        
        # Adapt rules for PostgreSQL (remove BigQuery-specific references)
        postgres_rules = {}
        for rule_type, rules in business_rules.items():
            postgres_rules[rule_type] = [
                rule.replace('Master_new', 'your_table_name').replace('PJT_Master', 'your_project_table')
                for rule in rules
            ]
        
        if custom_rules:
            for rule_type, rules in custom_rules.items():
                if rule_type in postgres_rules:
                    postgres_rules[rule_type].extend(rules)
                else:
                    postgres_rules[rule_type] = rules
        
        return {
            'allowed_tables': allowed_tables,
            'business_rules': postgres_rules
        }
    
    @classmethod
    def from_environment(cls, db_type: str = 'bigquery') -> Dict[str, Any]:
        """
        Load configuration from environment variables
        
        Args:
            db_type: Database type ('bigquery' or 'postgres')
            
        Returns:
            Dict containing configuration loaded from environment
        """
        # Load allowed tables from environment
        env_tables = os.environ.get(f'{db_type.upper()}_ALLOWED_TABLES', '')
        allowed_tables = [table.strip() for table in env_tables.split(',') if table.strip()] if env_tables else None
        
        # Load custom rules from environment (JSON format)
        import json
        env_rules = os.environ.get(f'{db_type.upper()}_BUSINESS_RULES', '')
        custom_rules = None
        if env_rules:
            try:
                custom_rules = json.loads(env_rules)
            except json.JSONDecodeError:
                print(f"⚠️ Warning: Invalid JSON in {db_type.upper()}_BUSINESS_RULES environment variable")
        
        if db_type.lower() == 'bigquery':
            return cls.get_bigquery_config(custom_tables=allowed_tables, custom_rules=custom_rules)
        else:
            return cls.get_postgres_config(custom_tables=allowed_tables, custom_rules=custom_rules)


# Example usage and documentation
EXAMPLE_USAGE = """
# Example 1: Using default BigQuery configuration
from vanna_config import VannaConfig

config = VannaConfig.get_bigquery_config()
provider_config = {
    'openai_api_key': 'your-key',
    'model': 'gpt-4',
    'path': './chroma_db',
    **config
}

# Example 2: Custom table whitelist
custom_tables = ['Master_new', 'PJT_Master', 'Additional_Table']
config = VannaConfig.get_bigquery_config(custom_tables=custom_tables)

# Example 3: Adding custom business rules
custom_rules = {
    'interview_rules': [
        "For senior interviews, filter by experience_level = 'Senior'"
    ],
    'custom_rules': [
        "Always include created_date in time-based queries"
    ]
}
config = VannaConfig.get_bigquery_config(custom_rules=custom_rules)

# Example 4: Loading from environment variables
# Set environment variables:
# export BIGQUERY_ALLOWED_TABLES="Master_new,PJT_Master,Custom_Table"
# export BIGQUERY_BUSINESS_RULES='{"interview_rules": ["Custom rule here"]}'
config = VannaConfig.from_environment('bigquery')
"""

if __name__ == "__main__":
    print("Vanna AI Configuration Examples:")
    print("=" * 50)
    print(EXAMPLE_USAGE)
