#!/usr/bin/env python3
"""
Simple test for VannaConfig functionality without requiring full app dependencies
"""

from vanna_config import VannaConfig
import json
import os


def test_basic_config():
    """Test basic configuration functionality"""
    print("🧪 Testing Basic Configuration")
    print("=" * 40)
    
    # Test default BigQuery config
    config = VannaConfig.get_bigquery_config()
    print(f"✅ Default BigQuery tables: {config['allowed_tables']}")
    print(f"✅ Business rule categories: {list(config['business_rules'].keys())}")
    
    # Test default PostgreSQL config
    pg_config = VannaConfig.get_postgres_config()
    print(f"✅ Default PostgreSQL tables: {pg_config['allowed_tables']}")
    
    print()


def test_custom_config():
    """Test custom configuration"""
    print("🧪 Testing Custom Configuration")
    print("=" * 40)
    
    # Test custom tables
    custom_tables = ['Master_new', 'PJT_Master', 'Custom_Table']
    custom_rules = {
        'interview_rules': [
            "Custom interview rule for testing"
        ],
        'custom_rules': [
            "Custom business rule",
            "Another custom rule"
        ]
    }
    
    config = VannaConfig.get_bigquery_config(
        custom_tables=custom_tables,
        custom_rules=custom_rules
    )
    
    print(f"✅ Custom tables: {config['allowed_tables']}")
    print(f"✅ Custom interview rules: {config['business_rules']['interview_rules'][-1]}")
    print(f"✅ Custom rules: {config['business_rules']['custom_rules']}")
    
    print()


def test_environment_config():
    """Test environment-based configuration"""
    print("🧪 Testing Environment Configuration")
    print("=" * 40)
    
    # Set test environment variables
    os.environ['BIGQUERY_ALLOWED_TABLES'] = 'Master_new,PJT_Master,Env_Table'
    os.environ['BIGQUERY_BUSINESS_RULES'] = json.dumps({
        'custom_rules': ['Environment rule 1', 'Environment rule 2'],
        'interview_rules': ['Environment interview rule']
    })
    
    try:
        config = VannaConfig.from_environment('bigquery')
        print(f"✅ Environment tables: {config['allowed_tables']}")
        print(f"✅ Environment custom rules: {config['business_rules']['custom_rules']}")
        print(f"✅ Environment interview rules: {config['business_rules']['interview_rules'][-1]}")
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        # Clean up environment variables
        if 'BIGQUERY_ALLOWED_TABLES' in os.environ:
            del os.environ['BIGQUERY_ALLOWED_TABLES']
        if 'BIGQUERY_BUSINESS_RULES' in os.environ:
            del os.environ['BIGQUERY_BUSINESS_RULES']
    
    print()


def test_business_rules():
    """Test business rules content"""
    print("🧪 Testing Business Rules Content")
    print("=" * 40)
    
    config = VannaConfig.get_bigquery_config()
    rules = config['business_rules']
    
    print("📋 Interview Rules:")
    for i, rule in enumerate(rules['interview_rules'], 1):
        print(f"   {i}. {rule}")
    
    print("\n📋 Count Rules:")
    for i, rule in enumerate(rules['count_rules'], 1):
        print(f"   {i}. {rule}")
    
    print("\n📋 General Rules:")
    for i, rule in enumerate(rules['general_rules'], 1):
        print(f"   {i}. {rule}")
    
    print("\n📋 Field-Specific Rules:")
    for i, rule in enumerate(rules['field_specific_rules'], 1):
        print(f"   {i}. {rule}")
    
    print()


def test_filtering_simulation():
    """Simulate how table filtering would work"""
    print("🧪 Simulating Table Filtering")
    print("=" * 40)
    
    config = VannaConfig.get_bigquery_config()
    allowed_tables = config['allowed_tables']
    
    # Simulate DDL items that might come from Vanna
    mock_ddl_items = [
        "CREATE TABLE Master_new (id INT, candidate_expert STRING)",
        "CREATE TABLE PJT_Master (project_id INT, name STRING)",
        "CREATE TABLE temp_table_123 (temp_data STRING)",
        "CREATE TABLE backup_data (old_data STRING)",
        "CREATE TABLE test_table (test_field INT)"
    ]
    
    print("📊 Mock DDL items to filter:")
    for i, ddl in enumerate(mock_ddl_items, 1):
        print(f"   {i}. {ddl}")
    
    # Simulate filtering logic
    filtered_ddl = []
    for ddl_item in mock_ddl_items:
        ddl_text = ddl_item.lower()
        for allowed_table in allowed_tables:
            if allowed_table.lower() in ddl_text:
                filtered_ddl.append(ddl_item)
                break
    
    print(f"\n🔍 Filtering result: {len(mock_ddl_items)} -> {len(filtered_ddl)} items")
    print(f"✅ Allowed tables: {allowed_tables}")
    print("\n📋 Filtered DDL items:")
    for i, ddl in enumerate(filtered_ddl, 1):
        print(f"   {i}. {ddl}")
    
    print()


def test_query_pattern_matching():
    """Test how business rules would be applied to different questions"""
    print("🧪 Testing Query Pattern Matching")
    print("=" * 40)
    
    config = VannaConfig.get_bigquery_config()
    business_rules = config['business_rules']
    
    test_questions = [
        "How many interviews are there?",
        "Count expert interviews",
        "How many candidates?",
        "Show me projects",
        "Count all records"
    ]
    
    print("📋 Test Questions and Expected Rule Application:")
    print()
    
    for question in test_questions:
        question_lower = question.lower()
        applicable_rules = []
        
        # Check for interview-related queries
        if any(keyword in question_lower for keyword in ['interview', 'expert', 'candidate']):
            applicable_rules.extend(business_rules['interview_rules'])
        
        # Check for count/number queries
        if any(keyword in question_lower for keyword in ['count', 'number', 'how many']):
            applicable_rules.extend(business_rules['count_rules'])
        
        # Always add general rules
        applicable_rules.extend(business_rules['general_rules'])
        
        print(f"❓ Question: '{question}'")
        print(f"📏 Applicable rules: {len(applicable_rules)}")
        if applicable_rules:
            print(f"   🎯 Key rule: {applicable_rules[0]}")
        print()


def main():
    """Run all tests"""
    print("🚀 VANNA AI CONFIGURATION TESTING")
    print("=" * 50)
    print()
    
    test_basic_config()
    test_custom_config()
    test_environment_config()
    test_business_rules()
    test_filtering_simulation()
    test_query_pattern_matching()
    
    print("=" * 50)
    print("✅ ALL TESTS COMPLETED SUCCESSFULLY!")
    print("=" * 50)
    print()
    print("🎯 Summary of Features Tested:")
    print("   1. ✅ Basic configuration loading")
    print("   2. ✅ Custom table and rule configuration")
    print("   3. ✅ Environment variable configuration")
    print("   4. ✅ Business rules content verification")
    print("   5. ✅ Table filtering simulation")
    print("   6. ✅ Query pattern matching simulation")
    print()
    print("📚 Next Steps:")
    print("   1. Integrate with your BigQuery/PostgreSQL agents")
    print("   2. Test with real Vanna AI queries")
    print("   3. Configure environment variables for production")
    print("   4. Customize rules for your specific use case")


if __name__ == "__main__":
    main()
