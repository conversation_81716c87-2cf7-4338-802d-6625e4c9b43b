# Vanna AI Table Filtering and Business Rules

This document explains the new table filtering and context-aware query instruction features implemented for Vanna AI in the chat-with-db project.

## Overview

The enhanced Vanna AI implementation now supports:

1. **Table Whitelist Filtering**: Restrict AI to only query specific allowed tables
2. **Context-Aware Business Rules**: Provide specific instructions for different query patterns
3. **Interview Query Optimization**: Automatic filtering for expert candidates
4. **Configurable Rules**: Easy customization through configuration files

## Features

### 1. Table Whitelist Filtering

The AI will only generate SQL queries using tables from a predefined whitelist, ignoring temporary tables and other unwanted tables.

**Default BigQuery Tables:**
- `Master_new` - Contains candidate and interview information
- `PJT_Master` - Contains project information

**Benefits:**
- Prevents queries against temporary or test tables
- Ensures data security by limiting table access
- Improves query reliability and performance

### 2. Context-Aware Business Rules

The AI receives specific instructions based on the type of query being asked:

#### Interview Rules
- For interview-related queries, always use `Master_new` table
- When counting interviews, filter by `Master_new.candidate_expert` containing 'Expert'
- Interview data is stored in `Master_new` table with `candidate_expert` field

#### Count Rules
- When counting interviews: `SELECT COUNT(*) FROM Master_new WHERE candidate_expert LIKE '%Expert%'`
- When counting projects, use `PJT_Master` table
- Always use appropriate WHERE clauses for counting specific types of records

#### General Rules
- Only query tables: `Master_new`, `PJT_Master`
- Ignore any temporary tables or tables with `_temp`, `_backup`, `_test` suffixes
- Use proper table aliases for readability

### 3. Field-Specific Instructions

The AI knows how to handle specific fields correctly:

- `candidate_expert` field: Use `LIKE '%Expert%'` to find expert candidates
- For interview counts: Filter `Master_new.candidate_expert` containing 'Expert'
- Project data: Use `PJT_Master` table for all project-related queries

## Implementation

### Custom Vanna Provider Classes

The implementation extends Vanna's base classes with custom filtering:

```python
class OpenAIVannaProvider(ChromaDB_VectorStore, OpenAI_Chat):
    def get_related_ddl(self, question: str, **kwargs) -> list:
        # Filter DDL based on allowed tables whitelist
        all_ddl = super().get_related_ddl(question, **kwargs)
        # ... filtering logic
        
    def get_sql_prompt(self, initial_prompt, question, question_sql_list, ddl_list, doc_list, **kwargs):
        # Add business rules to documentation
        # ... context-aware rule injection
```

### Configuration System

The `VannaConfig` class provides easy configuration management:

```python
from vanna_config import VannaConfig

# Default configuration
config = VannaConfig.get_bigquery_config()

# Custom configuration
config = VannaConfig.get_bigquery_config(
    custom_tables=['Master_new', 'PJT_Master', 'Additional_Table'],
    custom_rules={
        'interview_rules': ['Custom interview rule'],
        'custom_rules': ['Your custom rule here']
    }
)

# Environment-based configuration
config = VannaConfig.from_environment('bigquery')
```

## Usage Examples

### Basic Usage

```python
from app.agents.bigquery_agent import BigQueryChatAgent

# Initialize agent (automatically uses table filtering)
agent = BigQueryChatAgent(
    project_id="your-project-id",
    vanna_model_name="your-model",
    model="gpt-4"
)

# Connect and train
agent.connect()
agent.train()

# Ask questions - AI will automatically apply filtering and rules
sql = await agent.generate_sql("How many expert interviews are there?")
# Result: SELECT COUNT(*) FROM Master_new WHERE candidate_expert LIKE '%Expert%'
```

### Environment Configuration

Set environment variables for production deployment:

```bash
export BIGQUERY_ALLOWED_TABLES="Master_new,PJT_Master,Custom_Table"
export BIGQUERY_BUSINESS_RULES='{"interview_rules": ["Custom rule here"]}'
```

### Testing Queries

Example queries that demonstrate the filtering:

```python
test_questions = [
    "How many interviews are there?",
    # Expected: Uses Master_new with expert filtering
    
    "Count expert interviews", 
    # Expected: SELECT COUNT(*) FROM Master_new WHERE candidate_expert LIKE '%Expert%'
    
    "Show me projects",
    # Expected: Uses PJT_Master table
    
    "List all candidates",
    # Expected: Uses Master_new table only
]
```

## Benefits

### 1. Data Security
- Prevents access to sensitive or temporary tables
- Ensures queries only use approved data sources
- Reduces risk of data exposure

### 2. Query Accuracy
- Provides context-specific instructions for better SQL generation
- Ensures consistent field usage (e.g., expert filtering)
- Improves query reliability

### 3. Performance
- Limits queries to relevant tables only
- Reduces unnecessary table scans
- Improves overall system performance

### 4. Maintainability
- Centralized configuration management
- Easy to update rules and table lists
- Environment-specific configurations

## Configuration Options

### Table Filtering
```python
# BigQuery default tables
BIGQUERY_ALLOWED_TABLES = ['Master_new', 'PJT_Master']

# PostgreSQL (empty by default, customizable)
POSTGRES_ALLOWED_TABLES = []
```

### Business Rules Categories
- `interview_rules`: Rules for interview-related queries
- `count_rules`: Rules for counting operations
- `general_rules`: General query guidelines
- `field_specific_rules`: Field-specific instructions

### Environment Variables
- `BIGQUERY_ALLOWED_TABLES`: Comma-separated list of allowed tables
- `BIGQUERY_BUSINESS_RULES`: JSON string with custom business rules
- `POSTGRES_ALLOWED_TABLES`: PostgreSQL table whitelist
- `POSTGRES_BUSINESS_RULES`: PostgreSQL business rules

## Testing

Run the test script to verify functionality:

```bash
python test_table_filtering.py
```

The test script demonstrates:
- Table filtering in action
- Business rule application
- Configuration examples
- Query generation with filtering

## Troubleshooting

### Common Issues

1. **No tables in whitelist**: Ensure allowed_tables is properly configured
2. **Rules not applied**: Check business_rules configuration format
3. **Import errors**: Verify vanna_config.py is in the correct path

### Debug Information

The system provides debug output:
```
🔍 Filtered DDL: 10 -> 2 items (allowed tables: ['Master_new', 'PJT_Master'])
```

This shows how many DDL items were filtered and which tables are allowed.

## Future Enhancements

Potential improvements:
1. Dynamic table discovery and filtering
2. Role-based table access control
3. Query complexity analysis and optimization
4. Advanced business rule templating
5. Integration with data governance tools
