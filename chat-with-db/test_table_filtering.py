#!/usr/bin/env python3
"""
Test script for Vanna AI table filtering and business rules functionality

This script demonstrates how the new table filtering and context-aware
query instructions work with both BigQuery and PostgreSQL agents.
"""

import os
import asyncio
import json
from app.agents.bigquery_agent import BigQueryChatAgent
from app.agents.postgres_agent import PostgreSQLC<PERSON><PERSON>gent
from vanna_config import VannaConfig


async def test_bigquery_filtering():
    """Test BigQuery agent with table filtering"""
    print("=" * 60)
    print("TESTING BIGQUERY AGENT WITH TABLE FILTERING")
    print("=" * 60)
    
    # Initialize BigQuery agent
    project_id = os.environ.get("GOOGLE_CLOUD_PROJECT", "dashboard-383408")
    agent = BigQueryChatAgent(
        project_id=project_id,
        vanna_model_name="test-bigquery-filtering",
        model="gpt-4"
    )
    
    # Test connection
    print("\n1. Testing connection...")
    if not agent.connect():
        print("❌ Failed to connect to BigQuery")
        return
    
    # Test training
    print("\n2. Testing training...")
    if not agent.train():
        print("❌ Failed to train agent")
        return
    
    # Test queries that should work (allowed tables)
    print("\n3. Testing queries with allowed tables...")
    test_questions = [
        "How many interviews are there?",
        "Count the number of expert interviews",
        "Show me data from Master_new table",
        "What projects are in PJT_Master?",
        "How many expert candidates are there in Master_new?"
    ]
    
    for question in test_questions:
        print(f"\n🔍 Question: {question}")
        try:
            sql = await agent.generate_sql(question)
            print(f"✅ Generated SQL: {sql}")
            
            # Check if SQL contains only allowed tables
            sql_lower = sql.lower()
            allowed_tables = ['master_new', 'pjt_master']
            contains_allowed = any(table in sql_lower for table in allowed_tables)
            
            if contains_allowed:
                print("✅ SQL uses allowed tables")
            else:
                print("⚠️ SQL might not use allowed tables")
                
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n4. Testing business rules for interview queries...")
    interview_questions = [
        "How many interviews with experts?",
        "Count expert interviews",
        "Number of expert candidates"
    ]
    
    for question in interview_questions:
        print(f"\n🔍 Interview Question: {question}")
        try:
            sql = await agent.generate_sql(question)
            print(f"✅ Generated SQL: {sql}")
            
            # Check if SQL follows business rules for expert filtering
            if "candidate_expert" in sql.lower() and ("expert" in sql.lower() or "like" in sql.lower()):
                print("✅ SQL follows expert filtering business rule")
            else:
                print("⚠️ SQL might not follow expert filtering business rule")
                
        except Exception as e:
            print(f"❌ Error: {e}")


async def test_postgres_filtering():
    """Test PostgreSQL agent with table filtering"""
    print("\n" + "=" * 60)
    print("TESTING POSTGRESQL AGENT WITH TABLE FILTERING")
    print("=" * 60)
    
    # Note: This is a demonstration - actual connection would require real PostgreSQL credentials
    print("\n📝 PostgreSQL filtering demonstration (requires actual DB connection)")
    
    # Show how to configure custom table filtering for PostgreSQL
    custom_tables = ['users', 'companies', 'projects']
    custom_rules = {
        'interview_rules': [
            "For user interviews, use users table with role = 'candidate'",
            "Interview data is in users table with interview_status field"
        ]
    }
    
    config = VannaConfig.get_postgres_config(
        custom_tables=custom_tables,
        custom_rules=custom_rules
    )
    
    print(f"\n📋 Custom PostgreSQL Configuration:")
    print(f"Allowed Tables: {config['allowed_tables']}")
    print(f"Business Rules: {json.dumps(config['business_rules'], indent=2)}")


def test_configuration_examples():
    """Test different configuration scenarios"""
    print("\n" + "=" * 60)
    print("TESTING CONFIGURATION EXAMPLES")
    print("=" * 60)
    
    print("\n1. Default BigQuery Configuration:")
    default_config = VannaConfig.get_bigquery_config()
    print(f"Allowed Tables: {default_config['allowed_tables']}")
    print(f"Number of Business Rules: {len(default_config['business_rules'])}")
    
    print("\n2. Custom BigQuery Configuration:")
    custom_tables = ['Master_new', 'PJT_Master', 'Additional_Table']
    custom_rules = {
        'custom_rules': [
            "Always include timestamp in queries",
            "Use proper indexing for performance"
        ]
    }
    custom_config = VannaConfig.get_bigquery_config(
        custom_tables=custom_tables,
        custom_rules=custom_rules
    )
    print(f"Allowed Tables: {custom_config['allowed_tables']}")
    print(f"Custom Rules Added: {custom_config['business_rules'].get('custom_rules', [])}")
    
    print("\n3. Environment-based Configuration:")
    print("Set environment variables:")
    print("export BIGQUERY_ALLOWED_TABLES='Master_new,PJT_Master,Custom_Table'")
    print("export BIGQUERY_BUSINESS_RULES='{\"custom_rules\": [\"Environment rule\"]}'")
    
    # Simulate environment variables
    os.environ['BIGQUERY_ALLOWED_TABLES'] = 'Master_new,PJT_Master,Custom_Table'
    os.environ['BIGQUERY_BUSINESS_RULES'] = '{"custom_rules": ["Environment rule"]}'
    
    env_config = VannaConfig.from_environment('bigquery')
    print(f"Environment Tables: {env_config['allowed_tables']}")
    print(f"Environment Rules: {env_config['business_rules'].get('custom_rules', [])}")
    
    # Clean up environment variables
    del os.environ['BIGQUERY_ALLOWED_TABLES']
    del os.environ['BIGQUERY_BUSINESS_RULES']


def print_usage_examples():
    """Print usage examples and documentation"""
    print("\n" + "=" * 60)
    print("USAGE EXAMPLES")
    print("=" * 60)
    
    examples = """
# Example 1: Basic BigQuery agent with default filtering
from app.agents.bigquery_agent import BigQueryChatAgent

agent = BigQueryChatAgent(
    project_id="your-project-id",
    vanna_model_name="your-model",
    model="gpt-4"
)

# The agent will automatically use:
# - Allowed tables: ['Master_new', 'PJT_Master']
# - Business rules for interview and count queries

# Example 2: Custom table filtering
from vanna_config import VannaConfig

# Override default configuration
custom_config = VannaConfig.get_bigquery_config(
    custom_tables=['Master_new', 'PJT_Master', 'Additional_Table'],
    custom_rules={
        'custom_rules': ['Your custom business rule here']
    }
)

# Example 3: Environment-based configuration
# Set environment variables:
export BIGQUERY_ALLOWED_TABLES="Master_new,PJT_Master,Custom_Table"
export BIGQUERY_BUSINESS_RULES='{"interview_rules": ["Custom interview rule"]}'

# Then load configuration:
config = VannaConfig.from_environment('bigquery')

# Example 4: Testing specific queries
questions = [
    "How many interviews are there?",  # Should use Master_new with expert filtering
    "Count expert interviews",         # Should filter by candidate_expert LIKE '%Expert%'
    "Show me projects",               # Should use PJT_Master table
]

for question in questions:
    sql = await agent.generate_sql(question)
    print(f"Question: {question}")
    print(f"Generated SQL: {sql}")
"""
    
    print(examples)


async def main():
    """Main test function"""
    print("🧪 VANNA AI TABLE FILTERING AND BUSINESS RULES TEST")
    print("=" * 60)
    
    # Test configuration examples
    test_configuration_examples()
    
    # Test BigQuery filtering (if credentials are available)
    try:
        await test_bigquery_filtering()
    except Exception as e:
        print(f"⚠️ BigQuery test skipped (credentials not available): {e}")
    
    # Test PostgreSQL filtering (demonstration)
    await test_postgres_filtering()
    
    # Print usage examples
    print_usage_examples()
    
    print("\n✅ Test completed!")
    print("\n📋 Summary of new features:")
    print("1. ✅ Table whitelist filtering - only allowed tables are used")
    print("2. ✅ Context-aware business rules - specific instructions for query patterns")
    print("3. ✅ Interview query optimization - automatic expert filtering")
    print("4. ✅ Configurable rules - easy customization via VannaConfig")
    print("5. ✅ Environment variable support - production-ready configuration")


if __name__ == "__main__":
    asyncio.run(main())
