// Licensed to the Apache Software Foundation (ASF) under one
// or more contributor license agreements.  See the NOTICE file
// distributed with this work for additional information
// regarding copyright ownership.  The ASF licenses this file
// to you under the Apache License, Version 2.0 (the
// "License"); you may not use this file except in compliance
// with the License.  You may obtain a copy of the License at
//
//   http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing,
// software distributed under the License is distributed on an
// "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
// KIND, either express or implied.  See the License for the
// specific language governing permissions and limitations
// under the License.

#pragma once

#include <gtest/gtest.h>

// GTest < 1.11
#ifndef GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST
#  define GTEST_ALLOW_UNINSTANTIATED_PARAMETERIZED_TEST(A)
#endif
// GTest < 1.10
#ifndef TYPED_TEST_SUITE
#  define TYPED_TEST_SUITE TYPED_TEST_CASE
#  define TYPED_TEST_SUITE_P TYPED_TEST_CASE_P
#  define INSTANTIATE_TEST_SUITE_P INSTANTIATE_TEST_CASE_P
#  define REGISTER_TYPED_TEST_SUITE_P REGISTER_TYPED_TEST_CASE_P
#  define INSTANTIATE_TYPED_TEST_SUITE_P INSTANTIATE_TYPED_TEST_CASE_P
#endif
