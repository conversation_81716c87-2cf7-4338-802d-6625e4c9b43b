#!/bin/bash

# Cloud Run Deployment Script for Cha<PERSON> with DB AI Agent
# Make sure to run this script from the chat-with-db directory

set -e

# Configuration
PROJECT_ID="ais-prod-440309"  # Your Google Cloud Project ID
SERVICE_NAME="chat-with-db"
REGION="us-central1"
IMAGE_NAME="gcr.io/${PROJECT_ID}/${SERVICE_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Starting deployment of Chat with DB AI Agent to Cloud Run${NC}"

# Check if gcloud is installed
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI is not installed. Please install it first.${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker first.${NC}"
    exit 1
fi

# Set the project
echo -e "${YELLOW}📋 Setting Google Cloud project to ${PROJECT_ID}${NC}"
gcloud config set project ${PROJECT_ID}

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required Google Cloud APIs${NC}"
gcloud services enable cloudbuild.googleapis.com
gcloud services enable run.googleapis.com
gcloud services enable containerregistry.googleapis.com

# Build the Docker image for AMD64 (Cloud Run requirement)
echo -e "${YELLOW}🔨 Building Docker image for AMD64${NC}"
docker build --platform linux/amd64 -t ${IMAGE_NAME} .

# Push the image to Google Container Registry
echo -e "${YELLOW}📤 Pushing image to Google Container Registry${NC}"
docker push ${IMAGE_NAME}

# Check for required environment variables
echo -e "${YELLOW}🔍 Checking required environment variables${NC}"

# Try to get API keys from environment or .env file
if [ -z "$OPENAI_API_KEY" ] && [ -z "$GOOGLE_API_KEY" ] && [ -z "$VANNA_API_KEY" ]; then
    # Try to load from .env file if it exists
    if [ -f ".env" ]; then
        echo -e "${YELLOW}🔍 Loading environment variables from .env file${NC}"
        export $(grep -v '^#' .env | xargs)
    fi
fi

# Check if at least one API key is set
if [ -z "$OPENAI_API_KEY" ] && [ -z "$GOOGLE_API_KEY" ] && [ -z "$VANNA_API_KEY" ]; then
    echo -e "${RED}❌ No LLM API key is set${NC}"
    echo -e "${YELLOW}💡 Please set at least one of these API keys (Priority: OpenAI -> Google -> Vanna):${NC}"
    echo "  1. export OPENAI_API_KEY=your_openai_key_here"
    echo "  2. export GOOGLE_API_KEY=your_google_key_here"
    echo "  3. export VANNA_API_KEY=your_vanna_key_here"
    echo "  4. Add keys to .env file"
    exit 1
fi

# Show which API keys are available
echo -e "${GREEN}✅ Available LLM API keys:${NC}"
if [ ! -z "$OPENAI_API_KEY" ]; then
    echo "  🎯 OpenAI (Priority 1): ***${OPENAI_API_KEY: -4}"
fi
if [ ! -z "$GOOGLE_API_KEY" ]; then
    echo "  🔄 Google (Priority 2): ***${GOOGLE_API_KEY: -4}"
fi
if [ ! -z "$VANNA_API_KEY" ]; then
    echo "  📚 Vanna (Priority 3): ***${VANNA_API_KEY: -4}"
fi

# Deploy to Cloud Run
echo -e "${YELLOW}🚀 Deploying to Cloud Run${NC}"

gcloud run deploy ${SERVICE_NAME} \
    --image ${IMAGE_NAME} \
    --platform managed \
    --region ${REGION} \
    --allow-unauthenticated \
    --memory 2Gi \
    --cpu 2 \
    --timeout 3600 \
    --concurrency 80 \
    --max-instances 10 \
    --set-env-vars "ENVIRONMENT=production,OPENAI_API_KEY=${OPENAI_API_KEY},GOOGLE_API_KEY=${GOOGLE_API_KEY},VANNA_API_KEY=${VANNA_API_KEY},GOOGLE_CLOUD_PROJECT=${PROJECT_ID}" \
    --service-account "${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com"

# Update CORS origins separately
echo -e "${YELLOW}🔧 Setting CORS origins${NC}"
gcloud run services update ${SERVICE_NAME} \
    --region ${REGION} \
    --update-env-vars "ALLOWED_ORIGINS=https://arches-kpi.vercel.app,https://localhost:3000,http://localhost:3000"

# Get the service URL
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)')

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}🌐 Service URL: ${SERVICE_URL}${NC}"
echo -e "${GREEN}📊 Health check: ${SERVICE_URL}/health${NC}"
echo -e "${GREEN}📚 API docs: ${SERVICE_URL}/docs${NC}"

echo -e "${YELLOW}📝 Next steps:${NC}"
echo "1. ✅ Environment variables have been set automatically"
echo "2. Configure the service account with necessary BigQuery permissions:"
echo "   gcloud projects add-iam-policy-binding ${PROJECT_ID} \\"
echo "     --member=\"serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com\" \\"
echo "     --role=\"roles/bigquery.dataViewer\""
echo "   gcloud projects add-iam-policy-binding ${PROJECT_ID} \\"
echo "     --member=\"serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com\" \\"
echo "     --role=\"roles/bigquery.jobUser\""
echo "3. Test the API endpoints"
echo "4. Set up monitoring and logging"

echo -e "${YELLOW}🧪 Testing deployment:${NC}"
echo "curl ${SERVICE_URL}/health"
echo "curl -X POST ${SERVICE_URL}/api/chat/ask -H \"Content-Type: application/json\" -d '{\"question\":\"Show me a sample query\",\"db_type\":\"bigquery\"}'"
