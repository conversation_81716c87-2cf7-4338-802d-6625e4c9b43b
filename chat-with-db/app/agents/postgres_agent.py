import os
import json
import pandas as pd
from datetime import datetime, date
from decimal import Decimal
from typing import Dict, Any, Optional, AsyncGenerator

from .base_agent import DatabaseChatAgent
from .llm_provider import LLMProviderFactory


def serialize_value(value):
    """Convert non-JSON serializable values to JSON serializable ones"""
    if isinstance(value, (datetime, date)):
        return value.isoformat()
    elif isinstance(value, Decimal):
        return float(value)
    elif value is None:
        return None
    else:
        return str(value)


class PostgresChatAgent(DatabaseChatAgent):
    """
    Implementation of DatabaseChatAgent for PostgreSQL using Vanna AI
    """

    def __init__(
        self,
        host: str,
        dbname: str,
        user: str,
        password: str,
        vanna_model_name: str = "postgres-model",
        vanna_api_key: str = None,
        port: int = 5432,
    ):
        """
        Initialize the PostgreSQL chat agent with Vanna AI

        Args:
            host: PostgreSQL host
            dbname: Database name
            user: Database user
            password: Database password
            vanna_model_name: Name for the Vanna model (used for ChromaDB collection)
            vanna_api_key: API key for LLM (deprecated, use environment variables instead)
            port: Database port (default: 5432)
        """
        self.host = host
        self.dbname = dbname
        self.user = user
        self.password = password
        self.port = port
        self.vanna_model_name = vanna_model_name
        self.is_connected = False
        self.is_trained = False

        # Prepare configuration for LLM provider factory
        chroma_path = f'./chroma_db_{vanna_model_name}'
        provider_config = {
            'openai_api_key': vanna_api_key or os.environ.get("OPENAI_API_KEY"),
            'google_api_key': os.environ.get("GOOGLE_API_KEY"),
            'vanna_api_key': os.environ.get("VANNA_API_KEY"),
            'model': 'gpt-4',
            'path': chroma_path,
            # Table filtering configuration (can be customized per instance)
            'allowed_tables': [],  # Empty by default, can be set via environment or config
            # Business rules for specific query patterns
            'business_rules': {
                'interview_rules': [
                    "For interview-related queries, look for tables containing candidate or interview data",
                    "When counting interviews, filter by appropriate candidate type fields",
                    "Interview data may be stored in tables with candidate or expert information"
                ],
                'count_rules': [
                    "When counting specific types of records, use appropriate WHERE clauses",
                    "Always use COUNT(*) for counting rows",
                    "Consider filtering by status, type, or category fields when counting"
                ],
                'general_rules': [
                    "Avoid temporary tables or tables with _temp, _backup, _test suffixes",
                    "Use appropriate table aliases for readability",
                    "Consider using LIMIT for large result sets"
                ]
            }
        }

        # Get provider info for logging
        provider_info = LLMProviderFactory.get_provider_info(provider_config)
        print(f"🔍 Available LLM providers: {provider_info['total_available']}")
        if provider_info['primary_provider']:
            print(f"🎯 Using primary provider: {provider_info['primary_provider']}")

        for provider in provider_info['providers']:
            print(f"  - {provider['name']} (Priority {provider['priority']}): {provider['key_preview']}")

        # Initialize Vanna with flexible LLM provider
        self.vn = LLMProviderFactory.create_provider(provider_config)

    def connect(self) -> bool:
        """
        Connect to PostgreSQL using Vanna's built-in connection

        Returns:
            bool: True if connection was successful, False otherwise
        """
        try:
            # Use Vanna's built-in PostgreSQL connection
            self.vn.connect_to_postgres(
                host=self.host,
                dbname=self.dbname,
                user=self.user,
                password=self.password,
                port=self.port
            )

            # Test connection by running a simple query
            test_result = self.vn.run_sql("SELECT 1 as test")
            if test_result is not None:
                self.is_connected = True
                print(f"✅ Successfully connected to PostgreSQL database: {self.dbname}")
                return True
            else:
                self.is_connected = False
                return False
        except Exception as e:
            print(f"❌ Error connecting to PostgreSQL: {e}")
            self.is_connected = False
            return False

    def train(self, additional_context: Optional[str] = None) -> bool:
        """
        Train the agent on the PostgreSQL schema using Vanna AI

        Args:
            additional_context: Optional additional documentation or context to train on

        Returns:
            bool: True if training was successful, False otherwise
        """
        if not self.is_connected:
            print("❌ Not connected to PostgreSQL. Call connect() first.")
            return False

        try:
            print("🔄 Training Vanna AI on PostgreSQL schema...")

            # Fetch schema information from PostgreSQL using Vanna
            df_schema = self.vn.run_sql("""
                SELECT
                    table_schema,
                    table_name,
                    column_name,
                    data_type,
                    is_nullable,
                    column_default
                FROM
                    information_schema.columns
                WHERE
                    table_schema NOT IN ('pg_catalog', 'information_schema')
                ORDER BY table_schema, table_name, ordinal_position
            """)

            if df_schema is None or df_schema.empty:
                print("❌ No schema information found")
                return False

            print(f"📊 Found {len(df_schema)} columns across {df_schema['table_name'].nunique()} tables")

            # Train on DDL statements instead of using training plan
            # Get table information and create DDL statements
            tables = df_schema.groupby(['table_schema', 'table_name'])

            for (schema, table), group in tables:
                # Create DDL statement for each table
                columns_info = []
                for _, row in group.iterrows():
                    col_def = f"{row['column_name']} {row['data_type']}"
                    if row['is_nullable'] == 'NO':
                        col_def += " NOT NULL"
                    if pd.notna(row['column_default']):
                        col_def += f" DEFAULT {row['column_default']}"
                    columns_info.append(col_def)

                ddl = f"CREATE TABLE {schema}.{table} (\n    " + ",\n    ".join(columns_info) + "\n);"

                # Train Vanna on this DDL
                self.vn.train(ddl=ddl)
                print(f"✅ Trained on table: {schema}.{table}")

            print("✅ Successfully trained on database schema")

            # Add additional context if provided
            if additional_context:
                self.vn.train(documentation=additional_context)
                print("✅ Successfully added additional context")

            # Add some common business context
            business_context = """
            This is a business database containing information about:
            - Companies and their details
            - Users and their roles within companies
            - Tasks and their statuses
            - Various business operations and relationships
            """
            self.vn.train(documentation=business_context)

            self.is_trained = True
            print("🎉 Training completed successfully!")
            return True

        except Exception as e:
            print(f"❌ Error training on PostgreSQL schema: {e}")
            return False

    async def generate_sql(self, question: str) -> str:
        """
        Generate SQL for the given natural language question using Vanna AI

        Args:
            question: The natural language question to convert to SQL

        Returns:
            str: The generated SQL query
        """
        if not self.is_trained:
            raise ValueError("❌ Agent not trained. Call train() first.")

        try:
            print(f"🤖 Generating SQL for: {question}")

            # Use Vanna's AI to generate SQL
            sql = self.vn.generate_sql(question)

            print(f"✅ Generated SQL: {sql}")
            return sql

        except Exception as e:
            print(f"❌ Error generating SQL: {e}")
            # Fallback to a simple query if AI generation fails
            return "SELECT 'Error generating SQL. Please try a different question.' as message"

    def _generate_count_query(self, question_lower: str) -> str:
        """Generate COUNT queries based on the question"""
        # Look for table names in the question
        if self.schema_info and self.schema_info['tables']:
            table_names = list(self.schema_info['tables'].keys())

            # Check for specific table mentions
            for table_key in table_names:
                table_name = table_key.split('.')[-1]  # Get table name without schema

                # Check various forms of table names
                if (table_name in question_lower or
                    table_name.replace('_', ' ') in question_lower or
                    table_name.replace('_', '') in question_lower):
                    return f"SELECT COUNT(*) as total_{table_name} FROM {table_key}"

                # Check for plural/singular variations
                if table_name.endswith('s') and table_name[:-1] in question_lower:
                    return f"SELECT COUNT(*) as total_{table_name} FROM {table_key}"
                elif not table_name.endswith('s') and f"{table_name}s" in question_lower:
                    return f"SELECT COUNT(*) as total_{table_name} FROM {table_key}"

            # Check for common entity names
            if "compan" in question_lower:
                for table_key in table_names:
                    if "company" in table_key:
                        return f"SELECT COUNT(*) as total_companies FROM {table_key}"

            if "user" in question_lower:
                for table_key in table_names:
                    if "user" in table_key:
                        return f"SELECT COUNT(*) as total_users FROM {table_key}"

            if "task" in question_lower:
                for table_key in table_names:
                    if "task" in table_key:
                        return f"SELECT COUNT(*) as total_tasks FROM {table_key}"

            if "role" in question_lower:
                for table_key in table_names:
                    if "role" in table_key:
                        return f"SELECT COUNT(*) as total_roles FROM {table_key}"

            # Default to first table if no specific match
            first_table = table_names[0]
            return f"SELECT COUNT(*) as total_records FROM {first_table}"

        return "SELECT 1 as demo_count"

    def _generate_list_query(self, question_lower: str) -> str:
        """Generate SELECT queries to list/show data"""
        if self.schema_info and self.schema_info['tables']:
            table_names = list(self.schema_info['tables'].keys())

            # Look for specific table mentions
            for table_key in table_names:
                table_name = table_key.split('.')[-1]

                if (table_name in question_lower or
                    table_name.replace('_', ' ') in question_lower):
                    return f"SELECT * FROM {table_key} LIMIT 10"

            # Check for common entity names
            if "compan" in question_lower:
                for table_key in table_names:
                    if "company" in table_key and not "user" in table_key and not "role" in table_key:
                        return f"SELECT * FROM {table_key} LIMIT 10"

            if "user" in question_lower:
                for table_key in table_names:
                    if "user" in table_key:
                        return f"SELECT * FROM {table_key} LIMIT 10"

            # Default to first table
            first_table = table_names[0]
            return f"SELECT * FROM {first_table} LIMIT 5"

        return "SELECT 'No tables found' as message"

    def _generate_default_query(self, question_lower: str) -> str:
        """Generate default queries for unrecognized patterns"""
        if self.schema_info and self.schema_info['tables']:
            table_names = list(self.schema_info['tables'].keys())

            # Try to find relevant table based on keywords
            for table_key in table_names:
                table_name = table_key.split('.')[-1]
                if table_name in question_lower:
                    return f"SELECT * FROM {table_key} LIMIT 5"

            # Default to showing available tables
            return """
                SELECT table_name,
                       COUNT(*) as column_count
                FROM information_schema.columns
                WHERE table_schema NOT IN ('pg_catalog', 'information_schema')
                GROUP BY table_name
                ORDER BY table_name
            """

        return "SELECT 'Hello from PostgreSQL!' as message"

    async def execute_sql(self, sql: str) -> Dict[str, Any]:
        """
        Execute the given SQL query on PostgreSQL using Vanna

        Args:
            sql: The SQL query to execute

        Returns:
            Dict[str, Any]: The query results
        """
        if not self.is_connected:
            raise ValueError("❌ Not connected to PostgreSQL. Call connect() first.")

        try:
            print(f"🔄 Executing SQL: {sql}")

            # Use Vanna to execute the SQL
            results_df = self.vn.run_sql(sql)

            if results_df is None:
                return {"error": "Query returned no results"}

            # Convert DataFrame to dict for JSON serialization
            if isinstance(results_df, pd.DataFrame):
                # Apply serialization to all values
                serialized_df = results_df.applymap(serialize_value)
                results = serialized_df.to_dict(orient="records")
                columns = list(results_df.columns)
            else:
                results = [{"result": serialize_value(results_df)}]
                columns = ["result"]

            print(f"✅ Query executed successfully, returned {len(results)} rows")

            return {
                "results": results,
                "columns": columns,
                "row_count": len(results)
            }
        except Exception as e:
            print(f"❌ Error executing SQL: {e}")
            return {"error": str(e)}

    async def answer_query(self, question: str) -> Dict[str, Any]:
        """
        Generate SQL for the question, execute it, and return an answer using Vanna AI

        Args:
            question: The natural language question to answer

        Returns:
            Dict[str, Any]: The answer with SQL, results, and explanation
        """
        if not self.is_trained:
            return {"error": "❌ Agent not trained. Call train() first."}

        try:
            print(f"🎯 Answering query: {question}")

            # Generate SQL using Vanna AI
            sql = await self.generate_sql(question)

            # Execute SQL
            results = await self.execute_sql(sql)

            # Generate explanation using Vanna AI
            if "error" in results:
                explanation = f"There was an error executing the query: {results['error']}"
            else:
                try:
                    # Convert results back to DataFrame for Vanna's summary generation
                    if results.get('results'):
                        results_df = pd.DataFrame(results['results'])
                        explanation = self.vn.generate_summary(question, results_df)
                    else:
                        explanation = "The query executed successfully but returned no results."
                except Exception as e:
                    print(f"⚠️ Could not generate AI summary, using fallback: {e}")
                    row_count = results.get('row_count', 0)
                    explanation = f"I executed the SQL query and found {row_count} result(s). The query searched the database based on your question: '{question}'"

            print(f"✅ Query answered successfully")

            return {
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            }
        except Exception as e:
            print(f"❌ Error answering query: {e}")
            return {"error": str(e)}

    async def answer_query_stream(self, question: str) -> AsyncGenerator[str, None]:
        """
        Stream the answer to a question using Vanna AI

        Args:
            question: The natural language question to answer

        Yields:
            str: Chunks of the answer as they are generated
        """
        if not self.is_trained:
            yield json.dumps({"error": "❌ Agent not trained. Call train() first."})
            return

        try:
            # First yield a message that we're generating SQL
            yield json.dumps({"status": "generating_sql"})

            # Generate SQL using Vanna AI
            sql = await self.generate_sql(question)
            yield json.dumps({"sql": sql})

            # Execute SQL
            yield json.dumps({"status": "executing_sql"})
            results = await self.execute_sql(sql)
            yield json.dumps({"results": results})

            # Generate explanation using Vanna AI
            yield json.dumps({"status": "generating_explanation"})

            if "error" in results:
                explanation = f"There was an error executing the query: {results['error']}"
            else:
                try:
                    # Convert results back to DataFrame for Vanna's summary generation
                    if results.get('results'):
                        results_df = pd.DataFrame(results['results'])
                        explanation = self.vn.generate_summary(question, results_df)
                    else:
                        explanation = "The query executed successfully but returned no results."
                except Exception as e:
                    print(f"⚠️ Could not generate AI summary, using fallback: {e}")
                    row_count = results.get('row_count', 0)
                    explanation = f"I executed the SQL query and found {row_count} result(s). The query searched the database based on your question: '{question}'"

            # Yield the explanation in chunks for a streaming effect
            for i in range(0, len(explanation), 20):
                chunk = explanation[i:i+20]
                yield json.dumps({"explanation_chunk": chunk})

            # Final complete response
            yield json.dumps({
                "complete": True,
                "question": question,
                "sql": sql,
                "results": results,
                "explanation": explanation
            })
        except Exception as e:
            print(f"❌ Error in streaming answer: {e}")
            yield json.dumps({"error": str(e)})