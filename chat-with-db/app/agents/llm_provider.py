import os
import logging
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod

# Vanna imports
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

# Google imports
try:
    import google.generativeai as genai
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    genai = None

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""

    @abstractmethod
    def generate_sql(self, question: str) -> str:
        """Generate SQL from natural language question"""
        pass

    @abstractmethod
    def generate_summary(self, question: str, df) -> str:
        """Generate summary from question and results"""
        pass


class OpenAIVannaProvider(ChromaDB_VectorStore, OpenAI_Chat):
    """Vanna provider using OpenAI with custom table filtering"""

    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, config=config)

        # Configure table whitelist and business rules
        self.config = config or {}
        self.allowed_tables = self.config.get('allowed_tables', [])
        self.business_rules = self.config.get('business_rules', {})

    def get_related_ddl(self, question: str, **kwargs) -> list:
        """
        Override to filter DDL based on allowed tables whitelist
        """
        # Get all related DDL from parent class
        all_ddl = super().get_related_ddl(question, **kwargs)

        # If no whitelist is configured, return all DDL
        if not self.allowed_tables:
            return all_ddl

        # Filter DDL to only include allowed tables
        filtered_ddl = []
        for ddl_item in all_ddl:
            ddl_text = ddl_item if isinstance(ddl_item, str) else str(ddl_item)

            # Check if this DDL contains any allowed table
            for allowed_table in self.allowed_tables:
                if allowed_table.lower() in ddl_text.lower():
                    filtered_ddl.append(ddl_item)
                    break

        print(f"🔍 Filtered DDL: {len(all_ddl)} -> {len(filtered_ddl)} items (allowed tables: {self.allowed_tables})")
        return filtered_ddl

    def get_sql_prompt(self, initial_prompt, question, question_sql_list, ddl_list, doc_list, **kwargs):
        """
        Override to add custom business rules and context-aware instructions
        """
        # Add business rules to documentation
        if self.business_rules:
            business_rules_doc = self._format_business_rules(question)
            if business_rules_doc:
                doc_list = doc_list + [business_rules_doc]

        # Call parent method with enhanced documentation
        return super().get_sql_prompt(
            initial_prompt=initial_prompt,
            question=question,
            question_sql_list=question_sql_list,
            ddl_list=ddl_list,
            doc_list=doc_list,
            **kwargs
        )

    def _format_business_rules(self, question: str) -> str:
        """
        Format business rules based on the question context
        """
        question_lower = question.lower()
        rules_text = []

        # Check for interview-related queries
        if any(keyword in question_lower for keyword in ['interview', 'expert', 'candidate']):
            if 'interview_rules' in self.business_rules:
                rules_text.append("INTERVIEW QUERY RULES:")
                for rule in self.business_rules['interview_rules']:
                    rules_text.append(f"- {rule}")

        # Check for count/number queries
        if any(keyword in question_lower for keyword in ['count', 'number', 'how many']):
            if 'count_rules' in self.business_rules:
                rules_text.append("COUNT QUERY RULES:")
                for rule in self.business_rules['count_rules']:
                    rules_text.append(f"- {rule}")

        # Add general rules
        if 'general_rules' in self.business_rules:
            rules_text.append("GENERAL QUERY RULES:")
            for rule in self.business_rules['general_rules']:
                rules_text.append(f"- {rule}")

        return "\n".join(rules_text) if rules_text else ""


class GoogleVannaProvider(ChromaDB_VectorStore):
    """Vanna provider using Google Gemini API with custom table filtering"""

    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        self.config = config or {}
        self.api_key = self.config.get('api_key')
        self.model = self.config.get('model', 'gemini-1.5-flash')

        # Configure table whitelist and business rules
        self.allowed_tables = self.config.get('allowed_tables', [])
        self.business_rules = self.config.get('business_rules', {})

        if not GOOGLE_AVAILABLE:
            raise ImportError("Google Generative AI library not available. Install with: pip install google-generativeai")

        if not self.api_key:
            raise ValueError("Google API key is required")

        # Configure the Google API
        genai.configure(api_key=self.api_key)
        self.client = genai.GenerativeModel(self.model)

    def system_message(self, message: str) -> str:
        """Format system message for Google Gemini"""
        return f"System: {message}"

    def user_message(self, message: str) -> str:
        """Format user message for Google Gemini"""
        return f"User: {message}"

    def assistant_message(self, message: str) -> str:
        """Format assistant message for Google Gemini"""
        return f"Assistant: {message}"

    def get_related_ddl(self, question: str, **kwargs) -> list:
        """
        Override to filter DDL based on allowed tables whitelist
        """
        # Get all related DDL from parent class
        all_ddl = super().get_related_ddl(question, **kwargs)

        # If no whitelist is configured, return all DDL
        if not self.allowed_tables:
            return all_ddl

        # Filter DDL to only include allowed tables
        filtered_ddl = []
        for ddl_item in all_ddl:
            ddl_text = ddl_item if isinstance(ddl_item, str) else str(ddl_item)

            # Check if this DDL contains any allowed table
            for allowed_table in self.allowed_tables:
                if allowed_table.lower() in ddl_text.lower():
                    filtered_ddl.append(ddl_item)
                    break

        print(f"🔍 Filtered DDL: {len(all_ddl)} -> {len(filtered_ddl)} items (allowed tables: {self.allowed_tables})")
        return filtered_ddl

    def submit_prompt(self, prompt: str, **kwargs) -> str:
        """Submit prompt to Google Gemini and return response"""
        try:
            response = self.client.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            logger.error(f"Error submitting prompt to Google: {e}")
            raise

    def generate_sql(self, question: str, **kwargs) -> str:
        """Generate SQL using Google Gemini"""
        try:
            # Get relevant context from vector store
            context = self.get_related_ddl(question)

            prompt = f"""
            You are an expert SQL developer. Given the following database schema and a natural language question,
            generate a SQL query that answers the question.

            Database Schema:
            {context}

            Question: {question}

            Please provide only the SQL query without any explanation or markdown formatting.
            """

            response = self.client.generate_content(prompt)
            sql = response.text.strip()

            # Clean up the response
            if sql.startswith('```sql'):
                sql = sql[6:]
            if sql.endswith('```'):
                sql = sql[:-3]

            return sql.strip()

        except Exception as e:
            logger.error(f"Error generating SQL with Google: {e}")
            raise

    def generate_summary(self, question: str, df, **kwargs) -> str:
        """Generate summary using Google Gemini"""
        try:
            # Convert DataFrame to string representation
            df_str = df.to_string() if hasattr(df, 'to_string') else str(df)

            prompt = f"""
            Based on the following question and query results, provide a clear and concise summary:

            Question: {question}

            Results:
            {df_str}

            Please provide a natural language summary of what the data shows.
            """

            response = self.client.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating summary with Google: {e}")
            return f"Query executed successfully and returned {len(df) if hasattr(df, '__len__') else 'some'} results."


class LLMProviderFactory:
    """Factory for creating LLM providers with fallback logic"""

    @staticmethod
    def create_provider(config: Dict[str, Any]) -> Any:
        """
        Create an LLM provider with fallback logic
        Priority: OpenAI -> Google -> Vanna (local)

        Args:
            config: Configuration dictionary containing API keys and model settings

        Returns:
            Configured Vanna provider instance
        """
        # Extract configuration
        openai_key = config.get('openai_api_key') or os.environ.get('OPENAI_API_KEY')
        google_key = config.get('google_api_key') or os.environ.get('GOOGLE_API_KEY')
        vanna_key = config.get('vanna_api_key') or os.environ.get('VANNA_API_KEY')

        model = config.get('model', 'gpt-4')
        path = config.get('path', './chroma_db')

        # Try OpenAI first
        if openai_key:
            try:
                logger.info("🔄 Attempting to use OpenAI provider...")
                provider_config = {
                    'api_key': openai_key,
                    'model': model,
                    'path': path
                }
                provider = OpenAIVannaProvider(config=provider_config)
                logger.info("✅ Successfully initialized OpenAI provider")
                return provider
            except Exception as e:
                logger.warning(f"⚠️ OpenAI provider failed: {e}")

        # Try Google second
        if google_key and GOOGLE_AVAILABLE:
            try:
                logger.info("🔄 Attempting to use Google provider...")
                provider_config = {
                    'api_key': google_key,
                    'model': 'gemini-1.5-flash',
                    'path': path
                }
                provider = GoogleVannaProvider(config=provider_config)
                logger.info("✅ Successfully initialized Google provider")
                return provider
            except Exception as e:
                logger.warning(f"⚠️ Google provider failed: {e}")

        # Try Vanna local as last resort
        if vanna_key:
            try:
                logger.info("🔄 Attempting to use Vanna local provider...")
                provider_config = {
                    'api_key': vanna_key,
                    'model': model,
                    'path': path
                }
                provider = OpenAIVannaProvider(config=provider_config)
                logger.info("✅ Successfully initialized Vanna local provider")
                return provider
            except Exception as e:
                logger.warning(f"⚠️ Vanna local provider failed: {e}")

        # If all providers fail, raise an error
        available_keys = []
        if openai_key:
            available_keys.append("OpenAI")
        if google_key:
            available_keys.append("Google")
        if vanna_key:
            available_keys.append("Vanna")

        if not available_keys:
            raise ValueError(
                "❌ No API keys available. Please set one of: OPENAI_API_KEY, GOOGLE_API_KEY, or VANNA_API_KEY"
            )
        else:
            raise ValueError(
                f"❌ All available providers failed. Available keys: {', '.join(available_keys)}"
            )

    @staticmethod
    def get_provider_info(config: Dict[str, Any]) -> Dict[str, Any]:
        """Get information about which provider would be used"""
        openai_key = config.get('openai_api_key') or os.environ.get('OPENAI_API_KEY')
        google_key = config.get('google_api_key') or os.environ.get('GOOGLE_API_KEY')
        vanna_key = config.get('vanna_api_key') or os.environ.get('VANNA_API_KEY')

        providers = []

        if openai_key:
            providers.append({
                'name': 'OpenAI',
                'priority': 1,
                'available': True,
                'key_preview': f"***{openai_key[-4:]}" if openai_key else None
            })

        if google_key and GOOGLE_AVAILABLE:
            providers.append({
                'name': 'Google',
                'priority': 2,
                'available': True,
                'key_preview': f"***{google_key[-4:]}" if google_key else None
            })

        if vanna_key:
            providers.append({
                'name': 'Vanna',
                'priority': 3,
                'available': True,
                'key_preview': f"***{vanna_key[-4:]}" if vanna_key else None
            })

        return {
            'providers': providers,
            'primary_provider': providers[0]['name'] if providers else None,
            'total_available': len(providers)
        }
