import os
import logging
from typing import Optional, Dict, Any, List
from abc import ABC, abstractmethod

# Vanna imports
from vanna.openai.openai_chat import OpenAI_Chat
from vanna.chromadb.chromadb_vector import ChromaDB_VectorStore

# Google imports
try:
    import google.generativeai as genai
    GOOGLE_AVAILABLE = True
except ImportError:
    GOOGLE_AVAILABLE = False
    genai = None

logger = logging.getLogger(__name__)


class LLMProvider(ABC):
    """Abstract base class for LLM providers"""

    @abstractmethod
    def generate_sql(self, question: str) -> str:
        """Generate SQL from natural language question"""
        pass

    @abstractmethod
    def generate_summary(self, question: str, df) -> str:
        """Generate summary from question and results"""
        pass


class OpenAIVannaProvider(ChromaDB_VectorStore, OpenAI_Chat):
    """Vanna provider using OpenAI"""

    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        OpenAI_Chat.__init__(self, config=config)


class GoogleVannaProvider(ChromaDB_VectorStore):
    """Vanna provider using Google Gemini API"""

    def __init__(self, config=None):
        ChromaDB_VectorStore.__init__(self, config=config)
        self.config = config or {}
        self.api_key = self.config.get('api_key')
        self.model = self.config.get('model', 'gemini-1.5-flash')

        if not GOOGLE_AVAILABLE:
            raise ImportError("Google Generative AI library not available. Install with: pip install google-generativeai")

        if not self.api_key:
            raise ValueError("Google API key is required")

        # Configure the Google API
        genai.configure(api_key=self.api_key)
        self.client = genai.GenerativeModel(self.model)

    def system_message(self, message: str) -> str:
        """Format system message for Google Gemini"""
        return f"System: {message}"

    def user_message(self, message: str) -> str:
        """Format user message for Google Gemini"""
        return f"User: {message}"

    def assistant_message(self, message: str) -> str:
        """Format assistant message for Google Gemini"""
        return f"Assistant: {message}"

    def submit_prompt(self, prompt: str, **kwargs) -> str:
        """Submit prompt to Google Gemini and return response"""
        try:
            response = self.client.generate_content(prompt)
            return response.text.strip()
        except Exception as e:
            logger.error(f"Error submitting prompt to Google: {e}")
            raise

    def generate_sql(self, question: str, **kwargs) -> str:
        """Generate SQL using Google Gemini"""
        try:
            # Get relevant context from vector store
            context = self.get_related_ddl(question)

            prompt = f"""
            You are an expert SQL developer. Given the following database schema and a natural language question,
            generate a SQL query that answers the question.

            Database Schema:
            {context}

            Question: {question}

            Please provide only the SQL query without any explanation or markdown formatting.
            """

            response = self.client.generate_content(prompt)
            sql = response.text.strip()

            # Clean up the response
            if sql.startswith('```sql'):
                sql = sql[6:]
            if sql.endswith('```'):
                sql = sql[:-3]

            return sql.strip()

        except Exception as e:
            logger.error(f"Error generating SQL with Google: {e}")
            raise

    def generate_summary(self, question: str, df, **kwargs) -> str:
        """Generate summary using Google Gemini"""
        try:
            # Convert DataFrame to string representation
            df_str = df.to_string() if hasattr(df, 'to_string') else str(df)

            prompt = f"""
            Based on the following question and query results, provide a clear and concise summary:

            Question: {question}

            Results:
            {df_str}

            Please provide a natural language summary of what the data shows.
            """

            response = self.client.generate_content(prompt)
            return response.text.strip()

        except Exception as e:
            logger.error(f"Error generating summary with Google: {e}")
            return f"Query executed successfully and returned {len(df) if hasattr(df, '__len__') else 'some'} results."


class LLMProviderFactory:
    """Factory for creating LLM providers with fallback logic"""

    @staticmethod
    def create_provider(config: Dict[str, Any]) -> Any:
        """
        Create an LLM provider with fallback logic
        Priority: OpenAI -> Google -> Vanna (local)

        Args:
            config: Configuration dictionary containing API keys and model settings

        Returns:
            Configured Vanna provider instance
        """
        # Extract configuration
        openai_key = config.get('openai_api_key') or os.environ.get('OPENAI_API_KEY')
        google_key = config.get('google_api_key') or os.environ.get('GOOGLE_API_KEY')
        vanna_key = config.get('vanna_api_key') or os.environ.get('VANNA_API_KEY')

        model = config.get('model', 'gpt-4')
        path = config.get('path', './chroma_db')

        # Try OpenAI first
        if openai_key:
            try:
                logger.info("🔄 Attempting to use OpenAI provider...")
                provider_config = {
                    'api_key': openai_key,
                    'model': model,
                    'path': path
                }
                provider = OpenAIVannaProvider(config=provider_config)
                logger.info("✅ Successfully initialized OpenAI provider")
                return provider
            except Exception as e:
                logger.warning(f"⚠️ OpenAI provider failed: {e}")

        # Try Google second
        if google_key and GOOGLE_AVAILABLE:
            try:
                logger.info("🔄 Attempting to use Google provider...")
                provider_config = {
                    'api_key': google_key,
                    'model': 'gemini-1.5-flash',
                    'path': path
                }
                provider = GoogleVannaProvider(config=provider_config)
                logger.info("✅ Successfully initialized Google provider")
                return provider
            except Exception as e:
                logger.warning(f"⚠️ Google provider failed: {e}")

        # Try Vanna local as last resort
        if vanna_key:
            try:
                logger.info("🔄 Attempting to use Vanna local provider...")
                provider_config = {
                    'api_key': vanna_key,
                    'model': model,
                    'path': path
                }
                provider = OpenAIVannaProvider(config=provider_config)
                logger.info("✅ Successfully initialized Vanna local provider")
                return provider
            except Exception as e:
                logger.warning(f"⚠️ Vanna local provider failed: {e}")

        # If all providers fail, raise an error
        available_keys = []
        if openai_key:
            available_keys.append("OpenAI")
        if google_key:
            available_keys.append("Google")
        if vanna_key:
            available_keys.append("Vanna")

        if not available_keys:
            raise ValueError(
                "❌ No API keys available. Please set one of: OPENAI_API_KEY, GOOGLE_API_KEY, or VANNA_API_KEY"
            )
        else:
            raise ValueError(
                f"❌ All available providers failed. Available keys: {', '.join(available_keys)}"
            )

    @staticmethod
    def get_provider_info(config: Dict[str, Any]) -> Dict[str, Any]:
        """Get information about which provider would be used"""
        openai_key = config.get('openai_api_key') or os.environ.get('OPENAI_API_KEY')
        google_key = config.get('google_api_key') or os.environ.get('GOOGLE_API_KEY')
        vanna_key = config.get('vanna_api_key') or os.environ.get('VANNA_API_KEY')

        providers = []

        if openai_key:
            providers.append({
                'name': 'OpenAI',
                'priority': 1,
                'available': True,
                'key_preview': f"***{openai_key[-4:]}" if openai_key else None
            })

        if google_key and GOOGLE_AVAILABLE:
            providers.append({
                'name': 'Google',
                'priority': 2,
                'available': True,
                'key_preview': f"***{google_key[-4:]}" if google_key else None
            })

        if vanna_key:
            providers.append({
                'name': 'Vanna',
                'priority': 3,
                'available': True,
                'key_preview': f"***{vanna_key[-4:]}" if vanna_key else None
            })

        return {
            'providers': providers,
            'primary_provider': providers[0]['name'] if providers else None,
            'total_available': len(providers)
        }
