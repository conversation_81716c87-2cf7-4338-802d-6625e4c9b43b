#!/bin/bash

# Setup Environment and Deploy Script for Cha<PERSON> with DB AI Agent
# This script helps set up environment variables and deploy to Cloud Run

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${GREEN}🚀 Chat with DB AI Agent - Environment Setup and Deployment${NC}"
echo -e "${BLUE}================================================================${NC}"

# Configuration
PROJECT_ID="ais-prod-440309"
SERVICE_NAME="chat-with-db"
REGION="us-central1"

echo -e "${YELLOW}📋 Project Configuration:${NC}"
echo "  Project ID: ${PROJECT_ID}"
echo "  Service Name: ${SERVICE_NAME}"
echo "  Region: ${REGION}"
echo ""

# Try to get API keys from environment or .env file
if [ -z "$OPENAI_API_KEY" ] && [ -z "$GOOGLE_API_KEY" ] && [ -z "$VANNA_API_KEY" ]; then
    # Try to load from .env file if it exists
    if [ -f ".env" ]; then
        echo -e "${YELLOW}🔍 Loading environment variables from .env file${NC}"
        export $(grep -v '^#' .env | xargs)
    fi
fi

# Check if at least one API key is set
if [ -z "$OPENAI_API_KEY" ] && [ -z "$GOOGLE_API_KEY" ] && [ -z "$VANNA_API_KEY" ]; then
    echo -e "${RED}❌ No LLM API key is set${NC}"
    echo -e "${YELLOW}💡 Please set at least one of these API keys (Priority: OpenAI -> Google -> Vanna):${NC}"
    echo ""
    echo "Option 1 - OpenAI (Recommended):"
    echo "  export OPENAI_API_KEY=your_openai_api_key_here"
    echo "  🔑 Get your key from: https://platform.openai.com/api-keys"
    echo ""
    echo "Option 2 - Google Gemini:"
    echo "  export GOOGLE_API_KEY=your_google_api_key_here"
    echo "  🔑 Get your key from: https://makersuite.google.com/app/apikey"
    echo ""
    echo "Option 3 - Vanna (Local):"
    echo "  export VANNA_API_KEY=your_vanna_api_key_here"
    echo ""
    echo "Method - Add to your shell profile (~/.bashrc, ~/.zshrc, etc.):"
    echo "  echo 'export OPENAI_API_KEY=your_key_here' >> ~/.bashrc"
    echo "  source ~/.bashrc"
    echo ""
    exit 1
fi

# Show which API keys are available
echo -e "${GREEN}✅ Available LLM API keys:${NC}"
if [ ! -z "$OPENAI_API_KEY" ]; then
    echo "  🎯 OpenAI (Priority 1): ***${OPENAI_API_KEY: -4}"
fi
if [ ! -z "$GOOGLE_API_KEY" ]; then
    echo "  🔄 Google (Priority 2): ***${GOOGLE_API_KEY: -4}"
fi
if [ ! -z "$VANNA_API_KEY" ]; then
    echo "  📚 Vanna (Priority 3): ***${VANNA_API_KEY: -4}"
fi
echo ""

# Check if gcloud is installed and authenticated
echo -e "${YELLOW}🔍 Checking Google Cloud CLI...${NC}"
if ! command -v gcloud &> /dev/null; then
    echo -e "${RED}❌ gcloud CLI is not installed${NC}"
    echo -e "${YELLOW}💡 Please install it from: https://cloud.google.com/sdk/docs/install${NC}"
    exit 1
fi

# Check if authenticated
if ! gcloud auth list --filter=status:ACTIVE --format="value(account)" | grep -q .; then
    echo -e "${RED}❌ Not authenticated with Google Cloud${NC}"
    echo -e "${YELLOW}💡 Please run: gcloud auth login${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Google Cloud CLI is ready${NC}"

# Set the project
echo -e "${YELLOW}📋 Setting Google Cloud project...${NC}"
gcloud config set project ${PROJECT_ID}

# Check if service account exists
echo -e "${YELLOW}🔍 Checking service account...${NC}"
if ! gcloud iam service-accounts describe "${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" &> /dev/null; then
    echo -e "${YELLOW}🔧 Creating service account...${NC}"
    gcloud iam service-accounts create ${SERVICE_NAME} \
        --display-name="Chat with DB Service Account" \
        --description="Service account for Chat with DB AI Agent"
fi

# Grant necessary permissions
echo -e "${YELLOW}🔧 Setting up BigQuery permissions...${NC}"
gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/bigquery.dataViewer" \
    --quiet

gcloud projects add-iam-policy-binding ${PROJECT_ID} \
    --member="serviceAccount:${SERVICE_NAME}@${PROJECT_ID}.iam.gserviceaccount.com" \
    --role="roles/bigquery.jobUser" \
    --quiet

echo -e "${GREEN}✅ Service account and permissions configured${NC}"

# Enable required APIs
echo -e "${YELLOW}🔧 Enabling required Google Cloud APIs...${NC}"
gcloud services enable cloudbuild.googleapis.com --quiet
gcloud services enable run.googleapis.com --quiet
gcloud services enable containerregistry.googleapis.com --quiet
gcloud services enable bigquery.googleapis.com --quiet

echo -e "${GREEN}✅ APIs enabled${NC}"

# Now run the deployment script with environment variables
echo -e "${YELLOW}🚀 Starting deployment...${NC}"
echo ""
OPENAI_API_KEY="$OPENAI_API_KEY" GOOGLE_API_KEY="$GOOGLE_API_KEY" VANNA_API_KEY="$VANNA_API_KEY" ./deploy-cloud-run.sh

echo ""
echo -e "${GREEN}🎉 Setup and deployment completed!${NC}"
echo -e "${BLUE}================================================================${NC}"
echo -e "${YELLOW}📝 What was configured:${NC}"
echo "  ✅ LLM API keys configured (OpenAI -> Google -> Vanna priority)"
echo "  ✅ Google Cloud project configured"
echo "  ✅ Service account created with BigQuery permissions"
echo "  ✅ Required APIs enabled"
echo "  ✅ Application deployed to Cloud Run"
echo ""
echo -e "${YELLOW}🧪 Test your deployment:${NC}"
SERVICE_URL=$(gcloud run services describe ${SERVICE_NAME} --region=${REGION} --format='value(status.url)' 2>/dev/null || echo "https://chat-with-db-3sg2rkyq2q-uc.a.run.app")
echo "  Health check: curl ${SERVICE_URL}/health"
echo "  API docs: ${SERVICE_URL}/docs"
echo "  Test BigQuery: curl -X POST ${SERVICE_URL}/api/chat/ask -H \"Content-Type: application/json\" -d '{\"question\":\"Show me a sample query\",\"db_type\":\"bigquery\"}'"
