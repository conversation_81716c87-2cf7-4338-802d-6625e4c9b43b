#!/usr/bin/env python3
"""
Example usage of Vanna AI with table filtering and business rules

This script demonstrates how to use the new table filtering and 
context-aware query instruction features.
"""

import os
import asyncio
from app.agents.bigquery_agent import BigQueryChatAgent
from vanna_config import <PERSON>naConfig


async def example_basic_usage():
    """Example 1: Basic usage with default configuration"""
    print("=" * 60)
    print("EXAMPLE 1: BASIC USAGE WITH DEFAULT CONFIGURATION")
    print("=" * 60)
    
    # The BigQuery agent now automatically uses table filtering
    agent = BigQueryChatAgent(
        project_id=os.environ.get("GOOGLE_CLOUD_PROJECT", "dashboard-383408"),
        vanna_model_name="example-model",
        model="gpt-4"
    )
    
    print("✅ Agent created with default table filtering:")
    print("   - Allowed tables: Master_new, PJT_Master")
    print("   - Business rules: interview, count, general, field-specific")
    
    # Example questions that will be filtered and optimized
    example_questions = [
        "How many interviews are there?",
        "Count expert interviews", 
        "Show me projects",
        "How many expert candidates in Master_new?"
    ]
    
    print("\n📝 Example questions that will use filtering:")
    for i, question in enumerate(example_questions, 1):
        print(f"   {i}. {question}")
    
    print("\n💡 Expected behavior:")
    print("   - Interview questions will filter by candidate_expert LIKE '%Expert%'")
    print("   - Only Master_new and PJT_Master tables will be used")
    print("   - Temporary tables will be ignored")


def example_custom_configuration():
    """Example 2: Custom configuration"""
    print("\n" + "=" * 60)
    print("EXAMPLE 2: CUSTOM CONFIGURATION")
    print("=" * 60)
    
    # Create custom configuration
    custom_tables = ['Master_new', 'PJT_Master', 'Additional_Table']
    custom_rules = {
        'interview_rules': [
            "For senior interviews, also check experience_level = 'Senior'",
            "Include interview_date in time-based queries"
        ],
        'custom_rules': [
            "Always include created_date for audit purposes",
            "Use proper indexing hints for large tables"
        ]
    }
    
    config = VannaConfig.get_bigquery_config(
        custom_tables=custom_tables,
        custom_rules=custom_rules
    )
    
    print("✅ Custom configuration created:")
    print(f"   - Allowed tables: {config['allowed_tables']}")
    print(f"   - Custom interview rules: {len(config['business_rules']['interview_rules'])}")
    print(f"   - Custom rules: {config['business_rules']['custom_rules']}")
    
    print("\n💡 To use this configuration:")
    print("   1. Modify the provider_config in BigQueryChatAgent.__init__()")
    print("   2. Or set environment variables (see example 3)")


def example_environment_configuration():
    """Example 3: Environment-based configuration"""
    print("\n" + "=" * 60)
    print("EXAMPLE 3: ENVIRONMENT-BASED CONFIGURATION")
    print("=" * 60)
    
    print("🔧 Set environment variables for production:")
    print()
    print("export BIGQUERY_ALLOWED_TABLES='Master_new,PJT_Master,Custom_Table'")
    print("export BIGQUERY_BUSINESS_RULES='{")
    print('  "interview_rules": [')
    print('    "For VIP interviews, check priority_level = \'High\'"')
    print('  ],')
    print('  "custom_rules": [')
    print('    "Always log query execution time"')
    print('  ]')
    print("}'")
    print()
    
    # Demonstrate loading from environment
    print("📋 Loading configuration from environment:")
    
    # Simulate environment variables
    os.environ['BIGQUERY_ALLOWED_TABLES'] = 'Master_new,PJT_Master,Custom_Table'
    os.environ['BIGQUERY_BUSINESS_RULES'] = '{"custom_rules": ["Environment rule example"]}'
    
    try:
        env_config = VannaConfig.from_environment('bigquery')
        print(f"   ✅ Tables from env: {env_config['allowed_tables']}")
        print(f"   ✅ Rules from env: {env_config['business_rules'].get('custom_rules', [])}")
    except Exception as e:
        print(f"   ❌ Error loading from environment: {e}")
    finally:
        # Clean up
        if 'BIGQUERY_ALLOWED_TABLES' in os.environ:
            del os.environ['BIGQUERY_ALLOWED_TABLES']
        if 'BIGQUERY_BUSINESS_RULES' in os.environ:
            del os.environ['BIGQUERY_BUSINESS_RULES']


def example_query_patterns():
    """Example 4: Query patterns and expected results"""
    print("\n" + "=" * 60)
    print("EXAMPLE 4: QUERY PATTERNS AND EXPECTED RESULTS")
    print("=" * 60)
    
    patterns = [
        {
            "question": "How many interviews are there?",
            "expected_table": "Master_new",
            "expected_filter": "candidate_expert LIKE '%Expert%'",
            "explanation": "Interview questions automatically filter for experts"
        },
        {
            "question": "Count expert interviews",
            "expected_table": "Master_new", 
            "expected_filter": "candidate_expert LIKE '%Expert%'",
            "explanation": "Explicit expert mention reinforces filtering"
        },
        {
            "question": "Show me all projects",
            "expected_table": "PJT_Master",
            "expected_filter": "None (shows all projects)",
            "explanation": "Project queries use PJT_Master table"
        },
        {
            "question": "List candidates from temp_table",
            "expected_table": "Master_new (not temp_table)",
            "expected_filter": "Table filtering prevents temp table usage",
            "explanation": "Temporary tables are ignored, uses allowed table instead"
        }
    ]
    
    print("📋 Query Pattern Examples:")
    print()
    
    for i, pattern in enumerate(patterns, 1):
        print(f"{i}. Question: '{pattern['question']}'")
        print(f"   Expected Table: {pattern['expected_table']}")
        print(f"   Expected Filter: {pattern['expected_filter']}")
        print(f"   Explanation: {pattern['explanation']}")
        print()


def example_integration_code():
    """Example 5: Integration code snippets"""
    print("=" * 60)
    print("EXAMPLE 5: INTEGRATION CODE SNIPPETS")
    print("=" * 60)
    
    code_examples = '''
# 1. Basic integration with default filtering
from app.agents.bigquery_agent import BigQueryChatAgent

async def chat_with_filtered_ai():
    agent = BigQueryChatAgent(
        project_id="your-project-id",
        vanna_model_name="production-model",
        model="gpt-4"
    )
    
    if agent.connect() and agent.train():
        # AI will automatically apply table filtering and business rules
        sql = await agent.generate_sql("How many expert interviews?")
        results = await agent.execute_sql(sql)
        return results

# 2. Custom configuration for specific use cases
from vanna_config import VannaConfig

def create_custom_agent():
    # Define your specific tables and rules
    custom_config = VannaConfig.get_bigquery_config(
        custom_tables=['Master_new', 'PJT_Master', 'Your_Custom_Table'],
        custom_rules={
            'interview_rules': [
                "For technical interviews, check skill_level field",
                "Include interview_type in technical queries"
            ]
        }
    )
    
    # Use in your agent initialization
    # (requires modifying agent to accept custom config)
    return custom_config

# 3. Production deployment with environment config
import os

def production_setup():
    # Set in your deployment environment
    os.environ['BIGQUERY_ALLOWED_TABLES'] = 'Master_new,PJT_Master'
    os.environ['BIGQUERY_BUSINESS_RULES'] = json.dumps({
        'interview_rules': ['Production interview rules'],
        'security_rules': ['Log all queries', 'Validate user permissions']
    })
    
    # Load configuration
    config = VannaConfig.from_environment('bigquery')
    return config

# 4. Testing your filtering
async def test_filtering():
    agent = BigQueryChatAgent(project_id="test-project")
    
    test_cases = [
        "How many interviews?",  # Should filter for experts
        "Show temp_data table",  # Should ignore temp table
        "Count all projects",    # Should use PJT_Master
    ]
    
    for question in test_cases:
        sql = await agent.generate_sql(question)
        print(f"Q: {question}")
        print(f"SQL: {sql}")
        print("---")
'''
    
    print("💻 Integration Code Examples:")
    print(code_examples)


async def main():
    """Main example function"""
    print("🚀 VANNA AI TABLE FILTERING - USAGE EXAMPLES")
    print("=" * 60)
    
    # Run all examples
    await example_basic_usage()
    example_custom_configuration()
    example_environment_configuration()
    example_query_patterns()
    example_integration_code()
    
    print("\n" + "=" * 60)
    print("✅ EXAMPLES COMPLETED")
    print("=" * 60)
    print()
    print("🎯 Key Benefits:")
    print("   1. ✅ Automatic table filtering - only approved tables used")
    print("   2. ✅ Context-aware rules - smart query optimization")
    print("   3. ✅ Expert interview filtering - automatic candidate_expert filtering")
    print("   4. ✅ Easy configuration - simple setup and customization")
    print("   5. ✅ Production ready - environment variable support")
    print()
    print("📚 Next Steps:")
    print("   1. Review the configuration in vanna_config.py")
    print("   2. Test with your specific tables and rules")
    print("   3. Set up environment variables for production")
    print("   4. Run test_table_filtering.py to verify functionality")
    print()
    print("📖 Documentation: See VANNA_TABLE_FILTERING.md for full details")


if __name__ == "__main__":
    asyncio.run(main())
